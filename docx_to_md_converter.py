#!/usr/bin/env python3
"""
Convert .docx files to nicely formatted Markdown files
"""

import os
import re
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

def clean_text(text):
    """Clean and normalize text"""
    if not text:
        return ""
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    return text

def format_paragraph_as_markdown(paragraph):
    """Convert a paragraph to markdown format"""
    text = clean_text(paragraph.text)
    if not text:
        return ""
    
    # Check if it's a heading based on style or formatting
    style_name = paragraph.style.name.lower() if paragraph.style else ""
    
    # Handle different heading levels
    if 'heading 1' in style_name or 'title' in style_name:
        return f"# {text}\n\n"
    elif 'heading 2' in style_name:
        return f"## {text}\n\n"
    elif 'heading 3' in style_name:
        return f"### {text}\n\n"
    elif 'heading 4' in style_name:
        return f"#### {text}\n\n"
    elif 'heading 5' in style_name:
        return f"##### {text}\n\n"
    elif 'heading 6' in style_name:
        return f"###### {text}\n\n"
    
    # Check for bold formatting (potential headings)
    has_bold = any(run.bold for run in paragraph.runs if run.bold)
    if has_bold and len(text) < 100:  # Likely a heading if short and bold
        return f"## {text}\n\n"
    
    # Handle lists
    if paragraph.style and 'list' in paragraph.style.name.lower():
        return f"- {text}\n"
    
    # Check for numbered lists or bullet points in text
    if re.match(r'^\d+\.?\s+', text):
        # Numbered list
        return f"{text}\n"
    elif re.match(r'^[•·▪▫‣⁃]\s+', text) or text.startswith('- '):
        # Bullet list
        if not text.startswith('- '):
            text = f"- {text[2:]}"  # Replace bullet with markdown bullet
        return f"{text}\n"
    
    # Regular paragraph
    return f"{text}\n\n"

def format_table_as_markdown(table):
    """Convert a table to markdown format"""
    if not table.rows:
        return ""
    
    markdown_table = []
    
    # Process header row
    header_row = table.rows[0]
    header_cells = [clean_text(cell.text) for cell in header_row.cells]
    if any(header_cells):  # Only create table if there's content
        markdown_table.append("| " + " | ".join(header_cells) + " |")
        markdown_table.append("| " + " | ".join(["---"] * len(header_cells)) + " |")
        
        # Process data rows
        for row in table.rows[1:]:
            row_cells = [clean_text(cell.text) for cell in row.cells]
            if any(row_cells):  # Only add row if there's content
                markdown_table.append("| " + " | ".join(row_cells) + " |")
    
    if markdown_table:
        return "\n".join(markdown_table) + "\n\n"
    return ""

def convert_docx_to_markdown(docx_path, md_path):
    """Convert a .docx file to markdown"""
    try:
        doc = Document(docx_path)
        markdown_content = []
        
        # Add title based on filename
        filename = os.path.splitext(os.path.basename(docx_path))[0]
        markdown_content.append(f"# {filename}\n\n")
        
        # Process document elements
        for element in doc.element.body:
            if element.tag.endswith('p'):  # Paragraph
                # Find the corresponding paragraph object
                for para in doc.paragraphs:
                    if para._element == element:
                        md_text = format_paragraph_as_markdown(para)
                        if md_text.strip():
                            markdown_content.append(md_text)
                        break
            elif element.tag.endswith('tbl'):  # Table
                # Find the corresponding table object
                for table in doc.tables:
                    if table._element == element:
                        md_table = format_table_as_markdown(table)
                        if md_table.strip():
                            markdown_content.append(md_table)
                        break
        
        # Join all content and clean up
        final_content = "".join(markdown_content)
        
        # Clean up excessive newlines
        final_content = re.sub(r'\n{3,}', '\n\n', final_content)
        
        # Write to markdown file
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"✅ Converted: {docx_path} → {md_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error converting {docx_path}: {str(e)}")
        return False

def main():
    """Main function to convert all .docx files in current directory"""
    current_dir = os.getcwd()
    docx_files = [f for f in os.listdir(current_dir) if f.endswith('.docx')]
    
    if not docx_files:
        print("No .docx files found in current directory")
        return
    
    print(f"Found {len(docx_files)} .docx files to convert:")
    for file in docx_files:
        print(f"  - {file}")
    
    print("\nStarting conversion...\n")
    
    successful = 0
    failed = 0
    
    for docx_file in docx_files:
        md_file = os.path.splitext(docx_file)[0] + '.md'
        if convert_docx_to_markdown(docx_file, md_file):
            successful += 1
        else:
            failed += 1
    
    print(f"\n📊 Conversion Summary:")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Total files: {len(docx_files)}")

if __name__ == "__main__":
    main()
